#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成最终总结报告
"""

import pandas as pd
from datetime import datetime

def generate_final_report():
    """生成最终总结报告"""
    
    print("=" * 60)
    print("           日前价格匹配完成总结报告")
    print("=" * 60)
    
    # 1. 读取最终结果文件
    result_path = "6月电厂和售电结算表(中位数）_匹配日前价格.xlsx"
    price_data_path = "副本马桥大都市6月对账表（德沃浙熹）.xlsx"
    
    try:
        # 读取匹配结果
        result_df = pd.read_excel(result_path, sheet_name='日滚撮数据')
        price_df = pd.read_excel(price_data_path, sheet_name='Sheet7')
        
        print(f"\n📊 数据处理概况:")
        print(f"   • 原始价格数据: {len(price_df)} 行")
        print(f"   • 日滚撮数据: {len(result_df)} 行")
        print(f"   • 成功匹配: {len(result_df[result_df['日前价格'].notna()])} 行")
        print(f"   • 匹配成功率: {len(result_df[result_df['日前价格'].notna()]) / len(result_df) * 100:.1f}%")
        
        # 2. 日期覆盖范围
        price_df['日期'] = pd.to_datetime(price_df['日期'])
        result_df['标的日开始日期'] = pd.to_datetime(result_df['标的日开始日期'])
        
        print(f"\n📅 日期覆盖范围:")
        print(f"   • 日前价格数据: {price_df['日期'].min().date()} 至 {price_df['日期'].max().date()}")
        print(f"   • 日滚撮数据: {result_df['标的日开始日期'].min().date()} 至 {result_df['标的日开始日期'].max().date()}")
        
        # 3. 按日期统计匹配情况
        print(f"\n📈 按日期匹配统计:")
        date_stats = result_df.groupby(result_df['标的日开始日期'].dt.date).agg({
            '日前价格': ['count', lambda x: x.notna().sum()],
            '匹配状态': lambda x: (x != '未匹配').sum()
        })
        date_stats.columns = ['总行数', '匹配行数', '成功行数']
        
        for date, row in date_stats.iterrows():
            success_rate = row['匹配行数'] / row['总行数'] * 100 if row['总行数'] > 0 else 0
            print(f"   • {date}: {row['匹配行数']}/{row['总行数']} 行 ({success_rate:.0f}%)")
        
        # 4. 价格统计
        matched_data = result_df[result_df['日前价格'].notna()]
        if len(matched_data) > 0:
            print(f"\n💰 价格统计:")
            print(f"   • 日前价格范围: {matched_data['日前价格'].min():.2f} - {matched_data['日前价格'].max():.2f} 元/MWh")
            print(f"   • 日前价格平均: {matched_data['日前价格'].mean():.2f} 元/MWh")
            print(f"   • 市场中位数价格平均: {matched_data['市场中位数价格'].mean():.2f} 元/MWh")
            
            # 价格差异分析
            price_diff = matched_data['日前价格'] - matched_data['市场中位数价格']
            print(f"   • 价格差异平均: {price_diff.mean():.2f} 元/MWh")
            print(f"   • 价格差异范围: {price_diff.min():.2f} - {price_diff.max():.2f} 元/MWh")
        
        # 5. 处理的具体日期
        print(f"\n📋 处理的具体日期:")
        processed_dates = sorted(price_df['日期'].dt.date.unique())
        print("   原始数据来源:")
        print("   • 工作簿2.xlsx Sheet1-7: 2025-06-16 至 2025-06-22")
        print("   • 工作簿2.xlsx Sheet9-10: 2025-06-24, 2025-06-26")
        print("   • 副本马桥大都市6月对账表 Sheet7: 2025-06-23, 2025-06-25, 2025-06-27至2025-06-30")
        
        print(f"\n   实际处理的日期 ({len(processed_dates)} 天):")
        for i, date in enumerate(processed_dates):
            if i % 5 == 0:
                print("   ", end="")
            print(f"{date}", end="  ")
            if (i + 1) % 5 == 0:
                print()
        if len(processed_dates) % 5 != 0:
            print()
        
        # 6. 输出文件
        print(f"\n📁 生成的文件:")
        print(f"   • 主要输出: {result_path}")
        print(f"   • 价格数据: {price_data_path}")
        print(f"   • 处理脚本: process_electricity_prices.py, match_prices.py 等")
        
        # 7. 关键特点
        print(f"\n✨ 处理特点:")
        print(f"   • 半小时数据转小时数据: 通过相邻两个半小时价格平均值计算")
        print(f"   • 匹配策略: 基于标的日期的全天平均价格匹配")
        print(f"   • 数据完整性: 覆盖6月16-30日（除6月24、26日外的所有工作日）")
        print(f"   • 质量控制: 自动去重、数据验证、异常处理")
        
        print(f"\n" + "=" * 60)
        print("                  处理完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"生成报告时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    generate_final_report()
