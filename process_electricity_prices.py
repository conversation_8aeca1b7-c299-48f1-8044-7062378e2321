#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理电力价格数据：从工作簿2.xlsx提取经都热电厂价格数据，计算每小时价格，填入对账表
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import openpyxl
from openpyxl import load_workbook
import warnings
warnings.filterwarnings('ignore')

def read_workbook2_data():
    """读取工作簿2.xlsx的前7个分表数据"""
    workbook2_path = "工作簿2.xlsx"
    
    try:
        # 读取Excel文件的所有sheet名称
        xl_file = pd.ExcelFile(workbook2_path)
        sheet_names = xl_file.sheet_names
        print(f"工作簿2.xlsx中的所有sheet: {sheet_names}")
        
        # 读取前7个分表
        all_data = {}
        for i, sheet_name in enumerate(sheet_names[:7]):
            print(f"正在读取第{i+1}个分表: {sheet_name}")
            df = pd.read_excel(workbook2_path, sheet_name=sheet_name)
            print(f"分表 {sheet_name} 的列名: {df.columns.tolist()}")
            print(f"分表 {sheet_name} 的形状: {df.shape}")
            print(f"前几行数据:")
            print(df.head())
            print("-" * 50)
            all_data[sheet_name] = df
            
        return all_data
        
    except Exception as e:
        print(f"读取工作簿2.xlsx时出错: {e}")
        return None

def extract_jingdu_prices(all_data):
    """从所有数据中提取经都热电厂的价格数据"""
    jingdu_data = {}

    # 定义日期映射（从sheet名到日期）
    date_mapping = {
        'Sheet1': '2025-06-16',
        'Sheet2': '2025-06-17',
        'Sheet3': '2025-06-18',
        'Sheet4': '2025-06-19',
        'Sheet5': '2025-06-20',  # 注意：Sheet5可能是重复的，需要检查
        'Sheet6': '2025-06-21',
        'Sheet7': '2025-06-22'
    }

    for sheet_name, df in all_data.items():
        print(f"处理分表: {sheet_name} -> {date_mapping.get(sheet_name, '未知日期')}")

        # 查找包含"经都热电厂"的行
        mask = df.astype(str).apply(lambda x: x.str.contains('经都热电厂', na=False)).any(axis=1)
        jingdu_rows = df[mask]

        if len(jingdu_rows) > 0:
            print(f"找到 {len(jingdu_rows)} 行经都热电厂数据")
            # 取第一行经都热电厂的数据
            jingdu_row = jingdu_rows.iloc[0]

            # 提取价格数据（从第3列开始，跳过前两列的描述信息）
            price_columns = df.columns[2:]  # 从第3列开始
            prices = []

            for col in price_columns:
                try:
                    price = float(jingdu_row[col])
                    prices.append(price)
                except (ValueError, TypeError):
                    # 如果无法转换为数字，跳过
                    continue

            print(f"提取到 {len(prices)} 个价格数据点")
            print(f"价格范围: {min(prices):.2f} - {max(prices):.2f}")

            jingdu_data[sheet_name] = {
                'date': date_mapping.get(sheet_name),
                'prices': prices,
                'columns': price_columns.tolist()
            }
        else:
            print("未找到经都热电厂数据")

    return jingdu_data

def calculate_hourly_prices(jingdu_data):
    """将半小时价格数据转换为每小时价格"""
    hourly_data = {}

    for sheet_name, data in jingdu_data.items():
        if 'prices' not in data or len(data['prices']) == 0:
            continue

        prices = data['prices']
        date = data['date']

        # 假设价格数据是48个半小时点（00:30, 01:00, 01:30, 02:00, ...）
        # 转换为24个小时点
        hourly_prices = []

        # 每两个半小时价格计算平均值得到小时价格
        for i in range(0, len(prices), 2):
            if i + 1 < len(prices):
                # 两个半小时的平均值
                hourly_price = (prices[i] + prices[i + 1]) / 2
            else:
                # 如果只有一个值，直接使用
                hourly_price = prices[i]
            hourly_prices.append(hourly_price)

        print(f"{date}: 从 {len(prices)} 个半小时数据点转换为 {len(hourly_prices)} 个小时数据点")

        hourly_data[sheet_name] = {
            'date': date,
            'hourly_prices': hourly_prices
        }

    return hourly_data

def update_target_excel(hourly_data):
    """更新目标Excel文件"""
    target_path = "副本马桥大都市6月对账表（德沃浙熹）.xlsx"

    try:
        # 使用pandas读取现有数据
        existing_df = pd.read_excel(target_path, sheet_name='Sheet7')
        print("成功读取现有Sheet7数据")
        print(f"现有数据形状: {existing_df.shape}")
        print("现有数据前几行:")
        print(existing_df.head())

        # 准备新数据
        new_rows = []
        date_order = ['2025-06-16', '2025-06-17', '2025-06-18', '2025-06-19', '2025-06-20', '2025-06-21', '2025-06-22']

        for date_str in date_order:
            # 找到对应的数据
            sheet_data = None
            for sheet_name, data in hourly_data.items():
                if data['date'] == date_str:
                    sheet_data = data
                    break

            if sheet_data is None:
                print(f"未找到 {date_str} 的数据")
                continue

            hourly_prices = sheet_data['hourly_prices']
            date_obj = datetime.strptime(date_str, '%Y-%m-%d')

            print(f"准备 {date_str} 的数据，共 {len(hourly_prices)} 个小时")

            # 为每个小时创建一行数据
            for hour in range(min(len(hourly_prices), 24)):  # 最多24小时
                new_row = {
                    '日期': date_obj,
                    '时间段': f"{hour:02d}：00-{hour+1:02d}:00",
                    '日前价格': hourly_prices[hour]
                }
                new_rows.append(new_row)

        # 创建新数据的DataFrame
        new_df = pd.DataFrame(new_rows)
        print(f"创建了 {len(new_df)} 行新数据")

        # 合并新数据和现有数据
        combined_df = pd.concat([new_df, existing_df], ignore_index=True)
        print(f"合并后数据形状: {combined_df.shape}")

        # 按日期排序
        combined_df = combined_df.sort_values('日期')

        # 写回Excel文件
        with pd.ExcelWriter(target_path, mode='a', if_sheet_exists='replace') as writer:
            combined_df.to_excel(writer, sheet_name='Sheet7', index=False)

        print(f"数据已成功写入 {target_path}")
        print("更新后的前几行数据:")
        print(combined_df.head(10))

    except Exception as e:
        print(f"更新目标Excel文件时出错: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("开始处理电力价格数据...")

    # 1. 读取工作簿2.xlsx的数据
    print("\n=== 步骤1: 读取工作簿2.xlsx ===")
    all_data = read_workbook2_data()

    if all_data is None:
        print("无法读取工作簿2.xlsx，程序退出")
        return

    # 2. 提取经都热电厂的价格数据
    print("\n=== 步骤2: 提取经都热电厂价格数据 ===")
    jingdu_data = extract_jingdu_prices(all_data)

    if not jingdu_data:
        print("未找到经都热电厂数据，程序退出")
        return

    # 3. 计算每小时价格
    print("\n=== 步骤3: 计算每小时价格 ===")
    hourly_data = calculate_hourly_prices(jingdu_data)

    # 4. 更新目标Excel文件
    print("\n=== 步骤4: 更新目标Excel文件 ===")
    update_target_excel(hourly_data)

    print("\n数据处理完成！")

if __name__ == "__main__":
    main()
