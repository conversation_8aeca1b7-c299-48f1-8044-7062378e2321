#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将整理好的日前价格匹配到6月电厂和售电结算表的日滚撮数据中
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def load_processed_prices():
    """加载已处理的日前价格数据"""
    source_path = "副本马桥大都市6月对账表（德沃浙熹）.xlsx"
    
    try:
        # 读取Sheet7中的日前价格数据
        df = pd.read_excel(source_path, sheet_name='Sheet7')
        print(f"加载日前价格数据: {len(df)} 行")
        
        # 转换日期列
        df['日期'] = pd.to_datetime(df['日期'])
        
        # 显示数据范围
        print(f"日期范围: {df['日期'].min().date()} 到 {df['日期'].max().date()}")
        print(f"价格范围: {df['日前价格'].min():.2f} - {df['日前价格'].max():.2f}")
        
        return df
        
    except Exception as e:
        print(f"加载日前价格数据时出错: {e}")
        return None

def load_target_data():
    """加载目标文件的日滚撮数据"""
    target_path = "6月电厂和售电结算表(中位数）.xlsx"
    
    try:
        # 读取日滚撮数据
        df = pd.read_excel(target_path, sheet_name='日滚撮数据')
        print(f"加载日滚撮数据: {len(df)} 行")
        
        # 转换日期列
        df['操作日期'] = pd.to_datetime(df['操作日期'])
        df['标的日开始日期'] = pd.to_datetime(df['标的日开始日期'])
        df['标的日结束日期'] = pd.to_datetime(df['标的日结束日期'])
        
        # 显示数据结构
        print(f"列名: {df.columns.tolist()}")
        print(f"操作日期范围: {df['操作日期'].min().date()} 到 {df['操作日期'].max().date()}")
        print(f"标的日期范围: {df['标的日开始日期'].min().date()} 到 {df['标的日结束日期'].max().date()}")
        
        # 显示前几行数据
        print("\n前5行数据:")
        print(df[['操作日期', '标的日开始日期', '标的日结束日期', '分时段类型', '交易单元', '成交均价', '市场中位数价格']].head())
        
        return df
        
    except Exception as e:
        print(f"加载日滚撮数据时出错: {e}")
        return None

def match_prices(price_df, target_df):
    """匹配日前价格到日滚撮数据"""
    
    print("\n开始匹配日前价格...")
    
    # 创建日前价格的查找字典，按日期和小时
    price_dict = {}
    
    for _, row in price_df.iterrows():
        date = row['日期'].date()
        time_segment = row['时间段']
        price = row['日前价格']
        
        # 解析时间段，例如 "00：00-01:00" -> 0
        try:
            hour = int(time_segment.split('：')[0])
            if date not in price_dict:
                price_dict[date] = {}
            price_dict[date][hour] = price
        except:
            continue
    
    print(f"构建价格字典: {len(price_dict)} 个日期")
    
    # 为日滚撮数据添加日前价格列
    target_df['日前价格'] = np.nan
    target_df['匹配状态'] = '未匹配'
    
    matched_count = 0
    
    for idx, row in target_df.iterrows():
        # 使用标的日期进行匹配
        target_date = row['标的日开始日期'].date()
        time_type = row['分时段类型']
        
        # 根据分时段类型确定匹配的小时
        matched_price = None
        
        if pd.isna(time_type) or time_type == '全天':
            # 如果是全天，计算当天的平均价格
            if target_date in price_dict:
                daily_prices = list(price_dict[target_date].values())
                if daily_prices:
                    matched_price = np.mean(daily_prices)
                    target_df.at[idx, '匹配状态'] = '全天平均'
        else:
            # 尝试解析具体时段
            try:
                if '峰' in str(time_type):
                    # 峰时段，通常是8-11点和18-21点
                    peak_hours = [8, 9, 10, 18, 19, 20]
                    if target_date in price_dict:
                        peak_prices = [price_dict[target_date].get(h) for h in peak_hours if h in price_dict[target_date]]
                        if peak_prices:
                            matched_price = np.mean([p for p in peak_prices if p is not None])
                            target_df.at[idx, '匹配状态'] = '峰时段平均'
                elif '谷' in str(time_type):
                    # 谷时段，通常是23-7点
                    valley_hours = [23, 0, 1, 2, 3, 4, 5, 6, 7]
                    if target_date in price_dict:
                        valley_prices = [price_dict[target_date].get(h) for h in valley_hours if h in price_dict[target_date]]
                        if valley_prices:
                            matched_price = np.mean([p for p in valley_prices if p is not None])
                            target_df.at[idx, '匹配状态'] = '谷时段平均'
                elif '平' in str(time_type):
                    # 平时段，其他时间
                    flat_hours = [11, 12, 13, 14, 15, 16, 17, 21, 22]
                    if target_date in price_dict:
                        flat_prices = [price_dict[target_date].get(h) for h in flat_hours if h in price_dict[target_date]]
                        if flat_prices:
                            matched_price = np.mean([p for p in flat_prices if p is not None])
                            target_df.at[idx, '匹配状态'] = '平时段平均'
                else:
                    # 如果无法识别时段类型，使用全天平均
                    if target_date in price_dict:
                        daily_prices = list(price_dict[target_date].values())
                        if daily_prices:
                            matched_price = np.mean(daily_prices)
                            target_df.at[idx, '匹配状态'] = '全天平均(默认)'
            except:
                # 解析失败，使用全天平均
                if target_date in price_dict:
                    daily_prices = list(price_dict[target_date].values())
                    if daily_prices:
                        matched_price = np.mean(daily_prices)
                        target_df.at[idx, '匹配状态'] = '全天平均(解析失败)'
        
        if matched_price is not None:
            target_df.at[idx, '日前价格'] = matched_price
            matched_count += 1
    
    print(f"成功匹配 {matched_count} 行数据")
    
    # 显示匹配状态统计
    match_stats = target_df['匹配状态'].value_counts()
    print("\n匹配状态统计:")
    for status, count in match_stats.items():
        print(f"  {status}: {count} 行")
    
    return target_df

def save_results(target_df):
    """保存匹配结果"""
    original_path = "6月电厂和售电结算表(中位数）.xlsx"
    new_path = "6月电厂和售电结算表(中位数）_匹配日前价格.xlsx"

    try:
        # 读取原文件的所有sheet
        with pd.ExcelFile(original_path) as xls:
            all_sheets = {}
            for sheet_name in xls.sheet_names:
                if sheet_name == '日滚撮数据':
                    all_sheets[sheet_name] = target_df
                else:
                    all_sheets[sheet_name] = pd.read_excel(xls, sheet_name=sheet_name)

        # 写入新文件
        with pd.ExcelWriter(new_path) as writer:
            for sheet_name, df in all_sheets.items():
                df.to_excel(writer, sheet_name=sheet_name, index=False)

        print(f"\n结果已保存到 {new_path}")

        # 显示匹配结果样本
        print("\n匹配结果样本:")
        sample_cols = ['操作日期', '标的日开始日期', '分时段类型', '交易单元', '成交均价', '市场中位数价格', '日前价格', '匹配状态']
        print(target_df[sample_cols].head(10))

        # 显示匹配统计
        print(f"\n匹配统计:")
        print(f"总行数: {len(target_df)}")
        print(f"成功匹配: {len(target_df[target_df['匹配状态'] != '未匹配'])}")
        print(f"未匹配: {len(target_df[target_df['匹配状态'] == '未匹配'])}")

        # 显示未匹配的数据
        unmatched = target_df[target_df['匹配状态'] == '未匹配']
        if len(unmatched) > 0:
            print(f"\n未匹配的数据（前5行）:")
            print(unmatched[['操作日期', '标的日开始日期', '标的日结束日期', '分时段类型']].head())

    except Exception as e:
        print(f"保存结果时出错: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("开始匹配日前价格到日滚撮数据...")
    
    # 1. 加载已处理的日前价格数据
    print("\n=== 步骤1: 加载日前价格数据 ===")
    price_df = load_processed_prices()
    
    if price_df is None:
        print("无法加载日前价格数据，程序退出")
        return
    
    # 2. 加载目标文件的日滚撮数据
    print("\n=== 步骤2: 加载日滚撮数据 ===")
    target_df = load_target_data()
    
    if target_df is None:
        print("无法加载日滚撮数据，程序退出")
        return
    
    # 3. 执行价格匹配
    print("\n=== 步骤3: 执行价格匹配 ===")
    matched_df = match_prices(price_df, target_df)
    
    # 4. 保存结果
    print("\n=== 步骤4: 保存结果 ===")
    save_results(matched_df)
    
    print("\n价格匹配完成！")

if __name__ == "__main__":
    main()
