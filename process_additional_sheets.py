#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理工作簿2.xlsx中新增的sheet9和sheet10（6月24日和6月26日数据）
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def read_additional_sheets():
    """读取工作簿2.xlsx的sheet9和sheet10数据"""
    workbook2_path = "工作簿2.xlsx"
    
    try:
        # 读取Excel文件的所有sheet名称
        xl_file = pd.ExcelFile(workbook2_path)
        sheet_names = xl_file.sheet_names
        print(f"工作簿2.xlsx中的所有sheet: {sheet_names}")
        
        # 读取sheet9和sheet10
        additional_data = {}
        target_sheets = ['Sheet9', 'Sheet10']
        
        for sheet_name in target_sheets:
            if sheet_name in sheet_names:
                print(f"正在读取 {sheet_name}")
                df = pd.read_excel(workbook2_path, sheet_name=sheet_name)
                print(f"分表 {sheet_name} 的列名: {df.columns.tolist()}")
                print(f"分表 {sheet_name} 的形状: {df.shape}")
                print(f"前几行数据:")
                print(df.head())
                print("-" * 50)
                additional_data[sheet_name] = df
            else:
                print(f"未找到 {sheet_name}")
                
        return additional_data
        
    except Exception as e:
        print(f"读取工作簿2.xlsx时出错: {e}")
        return None

def extract_jingdu_prices_additional(additional_data):
    """从新增数据中提取经都热电厂的价格数据"""
    jingdu_data = {}
    
    # 定义日期映射
    date_mapping = {
        'Sheet9': '2025-06-24',
        'Sheet10': '2025-06-26'
    }
    
    for sheet_name, df in additional_data.items():
        print(f"处理分表: {sheet_name} -> {date_mapping.get(sheet_name, '未知日期')}")
        
        # 查找包含"经都热电厂"的行
        mask = df.astype(str).apply(lambda x: x.str.contains('经都热电厂', na=False)).any(axis=1)
        jingdu_rows = df[mask]
        
        if len(jingdu_rows) > 0:
            print(f"找到 {len(jingdu_rows)} 行经都热电厂数据")
            # 取第一行经都热电厂的数据
            jingdu_row = jingdu_rows.iloc[0]
            
            # 提取价格数据（从第3列开始，跳过前两列的描述信息）
            price_columns = df.columns[2:]  # 从第3列开始
            prices = []
            
            for col in price_columns:
                try:
                    price = float(jingdu_row[col])
                    prices.append(price)
                except (ValueError, TypeError):
                    # 如果无法转换为数字，跳过
                    continue
            
            print(f"提取到 {len(prices)} 个价格数据点")
            if prices:
                print(f"价格范围: {min(prices):.2f} - {max(prices):.2f}")
            
            jingdu_data[sheet_name] = {
                'date': date_mapping.get(sheet_name),
                'prices': prices,
                'columns': price_columns.tolist()
            }
        else:
            print("未找到经都热电厂数据")
    
    return jingdu_data

def calculate_hourly_prices_additional(jingdu_data):
    """将半小时价格数据转换为每小时价格"""
    hourly_data = {}
    
    for sheet_name, data in jingdu_data.items():
        if 'prices' not in data or len(data['prices']) == 0:
            continue
            
        prices = data['prices']
        date = data['date']
        
        # 假设价格数据是48个半小时点（00:30, 01:00, 01:30, 02:00, ...）
        # 转换为24个小时点
        hourly_prices = []
        
        # 每两个半小时价格计算平均值得到小时价格
        for i in range(0, len(prices), 2):
            if i + 1 < len(prices):
                # 两个半小时的平均值
                hourly_price = (prices[i] + prices[i + 1]) / 2
            else:
                # 如果只有一个值，直接使用
                hourly_price = prices[i]
            hourly_prices.append(hourly_price)
        
        print(f"{date}: 从 {len(prices)} 个半小时数据点转换为 {len(hourly_prices)} 个小时数据点")
        
        hourly_data[sheet_name] = {
            'date': date,
            'hourly_prices': hourly_prices
        }
    
    return hourly_data

def update_price_data(hourly_data):
    """更新日前价格数据文件"""
    target_path = "副本马桥大都市6月对账表（德沃浙熹）.xlsx"
    
    try:
        # 读取现有数据
        existing_df = pd.read_excel(target_path, sheet_name='Sheet7')
        print(f"现有数据: {len(existing_df)} 行")
        
        # 转换日期列
        existing_df['日期'] = pd.to_datetime(existing_df['日期'])
        
        # 准备新数据
        new_rows = []
        
        for sheet_name, data in hourly_data.items():
            date_str = data['date']
            hourly_prices = data['hourly_prices']
            date_obj = datetime.strptime(date_str, '%Y-%m-%d')
            
            print(f"准备 {date_str} 的数据，共 {len(hourly_prices)} 个小时")
            
            # 为每个小时创建一行数据
            for hour in range(min(len(hourly_prices), 24)):  # 最多24小时
                new_row = {
                    '日期': date_obj,
                    '时间段': f"{hour:02d}：00-{hour+1:02d}:00",
                    '日前价格': hourly_prices[hour]
                }
                new_rows.append(new_row)
        
        # 创建新数据的DataFrame
        new_df = pd.DataFrame(new_rows)
        print(f"创建了 {len(new_df)} 行新数据")
        
        # 合并新数据和现有数据
        combined_df = pd.concat([existing_df, new_df], ignore_index=True)
        print(f"合并后数据形状: {combined_df.shape}")
        
        # 按日期排序
        combined_df = combined_df.sort_values(['日期', '时间段'])
        
        # 去除重复数据（如果有的话）
        combined_df = combined_df.drop_duplicates(subset=['日期', '时间段'], keep='last')
        print(f"去重后数据形状: {combined_df.shape}")
        
        # 写回Excel文件
        with pd.ExcelWriter(target_path, mode='a', if_sheet_exists='replace') as writer:
            combined_df.to_excel(writer, sheet_name='Sheet7', index=False)
        
        print(f"数据已成功写入 {target_path}")
        
        # 显示新增日期的数据
        for sheet_name, data in hourly_data.items():
            date_str = data['date']
            date_obj = datetime.strptime(date_str, '%Y-%m-%d')
            day_data = combined_df[combined_df['日期'].dt.date == date_obj.date()]
            print(f"\n{date_str} 的数据 ({len(day_data)} 行):")
            print(day_data[['日期', '时间段', '日前价格']].head(3))
            if len(day_data) > 3:
                print("...")
                print(day_data[['日期', '时间段', '日前价格']].tail(1))
        
    except Exception as e:
        print(f"更新价格数据时出错: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("开始处理新增的6月24日和6月26日数据...")
    
    # 1. 读取新增的sheet9和sheet10数据
    print("\n=== 步骤1: 读取新增数据 ===")
    additional_data = read_additional_sheets()
    
    if not additional_data:
        print("无法读取新增数据，程序退出")
        return
    
    # 2. 提取经都热电厂的价格数据
    print("\n=== 步骤2: 提取经都热电厂价格数据 ===")
    jingdu_data = extract_jingdu_prices_additional(additional_data)
    
    if not jingdu_data:
        print("未找到经都热电厂数据，程序退出")
        return
    
    # 3. 计算每小时价格
    print("\n=== 步骤3: 计算每小时价格 ===")
    hourly_data = calculate_hourly_prices_additional(jingdu_data)
    
    # 4. 更新价格数据文件
    print("\n=== 步骤4: 更新价格数据文件 ===")
    update_price_data(hourly_data)
    
    print("\n新增数据处理完成！")

if __name__ == "__main__":
    main()
