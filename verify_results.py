#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证处理结果
"""

import pandas as pd
from datetime import datetime

def verify_results():
    """验证处理结果"""
    target_path = "副本马桥大都市6月对账表（德沃浙熹）.xlsx"
    
    try:
        # 读取Sheet7数据
        df = pd.read_excel(target_path, sheet_name='Sheet7')
        print(f"Sheet7总共有 {len(df)} 行数据")
        print(f"列名: {df.columns.tolist()}")
        
        # 转换日期列
        df['日期'] = pd.to_datetime(df['日期'])
        
        # 按日期分组统计
        date_counts = df.groupby(df['日期'].dt.date).size()
        print("\n各日期的数据行数:")
        for date, count in date_counts.items():
            print(f"{date}: {count} 行")
        
        # 显示6月16-22日的数据样本
        print("\n6月16-22日的数据样本:")
        june_16_22 = df[(df['日期'].dt.date >= datetime(2025, 6, 16).date()) & 
                        (df['日期'].dt.date <= datetime(2025, 6, 22).date())]
        
        print(f"6月16-22日共有 {len(june_16_22)} 行数据")
        
        # 按日期排序并显示每天的前几行
        june_16_22_sorted = june_16_22.sort_values(['日期', '时间段'])
        
        for date in pd.date_range('2025-06-16', '2025-06-22'):
            day_data = june_16_22_sorted[june_16_22_sorted['日期'].dt.date == date.date()]
            if len(day_data) > 0:
                print(f"\n{date.date()} ({len(day_data)} 行):")
                print(day_data.head(3).to_string(index=False))
                if len(day_data) > 3:
                    print("...")
                    print(day_data.tail(1).to_string(index=False))
        
        # 检查价格范围
        print(f"\n价格统计:")
        print(f"最小价格: {df['日前价格'].min():.2f}")
        print(f"最大价格: {df['日前价格'].max():.2f}")
        print(f"平均价格: {df['日前价格'].mean():.2f}")
        
    except Exception as e:
        print(f"验证时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_results()
