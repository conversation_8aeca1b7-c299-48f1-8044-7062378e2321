#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复重复数据问题
"""

import pandas as pd
from datetime import datetime

def fix_duplicates():
    """修复重复数据"""
    target_path = "副本马桥大都市6月对账表（德沃浙熹）.xlsx"
    
    try:
        # 读取Sheet7数据
        df = pd.read_excel(target_path, sheet_name='Sheet7')
        print(f"原始数据: {len(df)} 行")
        
        # 转换日期列
        df['日期'] = pd.to_datetime(df['日期'])
        
        # 去除重复数据，保留第一个
        df_cleaned = df.drop_duplicates(subset=['日期', '时间段'], keep='first')
        print(f"去重后数据: {len(df_cleaned)} 行")
        
        # 按日期和时间排序
        df_cleaned = df_cleaned.sort_values(['日期', '时间段'])
        
        # 检查6月16-22日的数据
        june_16_22 = df_cleaned[(df_cleaned['日期'].dt.date >= datetime(2025, 6, 16).date()) & 
                               (df_cleaned['日期'].dt.date <= datetime(2025, 6, 22).date())]
        
        print(f"6月16-22日数据: {len(june_16_22)} 行")
        
        # 按日期分组统计
        date_counts = df_cleaned.groupby(df_cleaned['日期'].dt.date).size()
        print("\n各日期的数据行数:")
        for date, count in date_counts.items():
            print(f"{date}: {count} 行")
        
        # 写回Excel文件
        with pd.ExcelWriter(target_path, mode='a', if_sheet_exists='replace') as writer:
            df_cleaned.to_excel(writer, sheet_name='Sheet7', index=False)
        
        print(f"\n数据已清理并写回 {target_path}")
        
        # 显示6月16-22日的样本数据
        print("\n6月16-22日样本数据:")
        for date in pd.date_range('2025-06-16', '2025-06-22'):
            day_data = june_16_22[june_16_22['日期'].dt.date == date.date()]
            if len(day_data) > 0:
                print(f"\n{date.date()} ({len(day_data)} 行):")
                print(day_data.head(3)[['日期', '时间段', '日前价格']].to_string(index=False))
                if len(day_data) > 3:
                    print("...")
                    print(day_data.tail(1)[['日期', '时间段', '日前价格']].to_string(index=False))
        
    except Exception as e:
        print(f"修复时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_duplicates()
