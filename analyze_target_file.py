#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析6月电厂和售电结算表文件结构
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def analyze_target_file():
    """分析目标文件结构"""
    target_path = "6月电厂和售电结算表(中位数）.xlsx"
    
    try:
        # 读取Excel文件的所有sheet名称
        xl_file = pd.ExcelFile(target_path)
        sheet_names = xl_file.sheet_names
        print(f"文件中的所有sheet: {sheet_names}")
        
        # 逐个查看每个sheet的结构
        for i, sheet_name in enumerate(sheet_names):
            print(f"\n=== Sheet {i+1}: {sheet_name} ===")
            try:
                df = pd.read_excel(target_path, sheet_name=sheet_name)
                print(f"形状: {df.shape}")
                print(f"列名: {df.columns.tolist()}")
                
                # 显示前几行数据
                print("前5行数据:")
                print(df.head())
                
                # 查找可能包含"日滚撮"的列或数据
                for col in df.columns:
                    if '日滚撮' in str(col) or '日前' in str(col) or '价格' in str(col):
                        print(f"找到相关列: {col}")
                
                # 检查数据内容中是否包含相关关键词
                for col in df.columns:
                    if df[col].astype(str).str.contains('日滚撮|日前|价格', na=False).any():
                        print(f"在列 {col} 中找到相关数据")
                        # 显示包含关键词的行
                        mask = df[col].astype(str).str.contains('日滚撮|日前|价格', na=False)
                        relevant_rows = df[mask]
                        if len(relevant_rows) > 0:
                            print("相关行:")
                            print(relevant_rows.head(3))
                
                print("-" * 50)
                
            except Exception as e:
                print(f"读取sheet {sheet_name} 时出错: {e}")
                continue
        
    except Exception as e:
        print(f"分析文件时出错: {e}")

if __name__ == "__main__":
    analyze_target_file()
