#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证匹配结果
"""

import pandas as pd
from datetime import datetime

def verify_match_results():
    """验证匹配结果"""
    result_path = "6月电厂和售电结算表(中位数）_匹配日前价格.xlsx"
    
    try:
        # 读取匹配后的日滚撮数据
        df = pd.read_excel(result_path, sheet_name='日滚撮数据')
        print(f"匹配后的日滚撮数据: {len(df)} 行")
        
        # 检查新增的列
        print(f"列名: {df.columns.tolist()}")
        
        # 转换日期列
        df['标的日开始日期'] = pd.to_datetime(df['标的日开始日期'])
        
        # 统计匹配情况
        print(f"\n匹配统计:")
        print(f"总行数: {len(df)}")
        print(f"有日前价格的行数: {len(df[df['日前价格'].notna()])}")
        print(f"无日前价格的行数: {len(df[df['日前价格'].isna()])}")
        
        # 按匹配状态统计
        match_stats = df['匹配状态'].value_counts()
        print(f"\n按匹配状态统计:")
        for status, count in match_stats.items():
            print(f"  {status}: {count} 行")
        
        # 按日期统计匹配情况
        print(f"\n按标的日期统计匹配情况:")
        date_stats = df.groupby(df['标的日开始日期'].dt.date).agg({
            '日前价格': ['count', lambda x: x.notna().sum()],
            '匹配状态': lambda x: (x != '未匹配').sum()
        }).round(2)
        
        date_stats.columns = ['总行数', '有价格行数', '匹配成功行数']
        print(date_stats)
        
        # 显示匹配成功的样本数据
        print(f"\n匹配成功的样本数据:")
        matched_data = df[df['匹配状态'] != '未匹配']
        sample_cols = ['操作日期', '标的日开始日期', '分时段类型', '交易单元', '成交均价', '市场中位数价格', '日前价格', '匹配状态']
        print(matched_data[sample_cols].head(10))
        
        # 显示价格对比
        print(f"\n价格对比统计:")
        matched_data = df[df['日前价格'].notna()]
        if len(matched_data) > 0:
            print(f"日前价格范围: {matched_data['日前价格'].min():.2f} - {matched_data['日前价格'].max():.2f}")
            print(f"日前价格平均: {matched_data['日前价格'].mean():.2f}")
            print(f"市场中位数价格范围: {matched_data['市场中位数价格'].min():.2f} - {matched_data['市场中位数价格'].max():.2f}")
            print(f"市场中位数价格平均: {matched_data['市场中位数价格'].mean():.2f}")
            
            # 计算价格差异
            matched_data['价格差异'] = matched_data['日前价格'] - matched_data['市场中位数价格']
            print(f"价格差异范围: {matched_data['价格差异'].min():.2f} - {matched_data['价格差异'].max():.2f}")
            print(f"价格差异平均: {matched_data['价格差异'].mean():.2f}")
        
        # 显示未匹配的数据
        unmatched_data = df[df['匹配状态'] == '未匹配']
        if len(unmatched_data) > 0:
            print(f"\n未匹配的数据 ({len(unmatched_data)} 行):")
            unmatched_dates = unmatched_data['标的日开始日期'].dt.date.value_counts()
            print("按日期统计:")
            for date, count in unmatched_dates.items():
                print(f"  {date}: {count} 行")
        
    except Exception as e:
        print(f"验证时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_match_results()
